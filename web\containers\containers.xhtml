<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/images/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <link type="text/css" href="../assets/css/flag-icon.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/servicos.css" rel="stylesheet"/>
            <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.8.2/css/all.css" integrity="sha384-oS3vJWv+0UjzBfQzYUhtDYW+Pj2yciDJxpsK1OYPAYjqT085Qq/1cq5FLXAZQ7Ay" crossorigin="anonymous"/>
            <script src="https://maps.google.com/maps/api/js?key=#{login.googleApiOper}" type="text/javascript" ></script>
            <script src="../assets/js/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <style>
                .AbrirFiltros{
                    position: absolute;
                    z-index:1 !important;
                    background-color:#000;
                    color:#FFF;
                    writing-mode: vertical-rl;
                    text-orientation: upright !important;
                    padding:10px 6px 10px 6px !important;
                    top:80px;
                    border-radius: 0px 8px 8px 0px;
                    box-shadow: 1px 1px 2px #666;
                    cursor: pointer;
                }
                               
                #divQuadroResumo .ui-selectonemenu-label,
                #divQuadroResumo > .ui-selectonemenu-label{
                    background-color:#CED2DA !important;
                    padding:5px 3px 6px 10px !important;
                    box-shadow:none !important;
                    border:none !important;
                }
                
                #divQuadroResumo{
                    position:absolute !important;
                    width:240px !important;
                    top:70px !important;
                    /*left:26px !important;*/
                    z-index:1 !important;
                    height: 100%;
                    max-height:500px !important;
                }
                
                #divQuadroResumo .FecharFiltros{
                    position:absolute;
                    right:-10px;
                    top:-15px;
                    color:#FFF;
                    background-color:#000;
                    width:35px;
                    height: 35px;
                    border-radius:50%;
                    text-align: center;
                    cursor: pointer;
                    padding-top: 5px;
                    padding-left: 1px;
                    font-size:17pt !important;
                    box-shadow:1px 1px 2px #666;
                }
                
                #divQuadroResumo .ItemResumo{
                    background-color: rgba(211, 221, 228, 0.8) !important;
                    width:100% !important;
                    border-radius:12px;
                    padding:1px 1px 0px 1px !important;
                    box-shadow:1px  1px 3px #666;
                    border-radius:4px;
                }
                
                #divQuadroResumo .FiltrosMapa{
                    width:100%;
                    padding:8px 4px 8px 4px !important;
                    outline:none;
                    border:thin solid #CCC;
                    background-color:#EEE;
                    color:#505050;
                    cursor:pointer
                }
                
                #divQuadroResumo input[type="checkbox"]{
                    padding:0 !important;
                    background-color:black;
                    position:absolute;
                    margin-top:5px !important;
                    border:thin solid #AAA !important;
                    border-radius:2px !important;
                    
                }
                
                #divQuadroResumo input[type="checkbox"] + label{
                    font-size:10pt;
                    font-weight:600;
                    color:#505050;
                    margin:0px !important;
                    padding:0px 0px 0px 16px !important;
                    cursor:pointer;
                    text-shadow:1px 1px rgba(255, 255, 255, 0.5);
                }
                
                #divQuadroResumo .Item,
                #divQuadroResumo .ItemZebrado{
                    position:relative;
                    width:calc(100% - 12px) !important;
                    margin-left: 6px !important;
                    padding:2px 8px 0px 8px !important;
                    border-radius:2px;
                }
                
                #divQuadroResumo .ItemZebrado{
                    background-color:rgba(187, 187, 187, 0.5) !important;
                }
                
                #divQuadroResumo #lblTituloFiltro{
                    font-size:8pt !important;
                    font-weight:600 !important;
                    color:#000 !important;
                    padding:0px 0px 0px 10px !important;
                    text-shadow:1px 1px rgba(255, 255, 255, 0.5);
                    border:none !important;
                }
                
                #divQuadroResumo .QdeStatus{
                    position: absolute;
                    width:45px;
                    height:18px !important;
                    border-radius:4px;
                    right:6px;
                    top: 5px;
                    font-size:8pt !important;
                    text-align:center;
                    color:#FFF;
                    font-weight:bold;
                    padding:0x !important;
                }
                
                #divQuadroResumo .QdeStatus[cor="R"]{
                    background-color:#ff0000;
                    border:thin solid #cc0000;
                }
                
                #divQuadroResumo .QdeStatus[cor="G"]{
                    background-color: #00cc00;
                    border:thin solid #009900;
                }
                
                #divQuadroResumo .QdeStatus[cor="Y"]{
                    background-color: #ffba00;
                    border:thin solid #cca300;
                }
                
                #divQuadroResumo .QdeStatus[cor="W"]{
                    background-color:#FFF;
                    color:#505050;
                }
                
                #divQuadroResumo .fa-fw{
                    float:left;
                    width:100%;
                    text-align:center;
                    margin-top:2px;
                }
                
                div[id*="infoWindowCamcambas"] .panelgrid .ui-widget{
                    width:100% !important;
                    z-index:2 !important;
                }
                
                div[id*="infoWindowCamcambas"] .ui-panelgrid-cell{
                    text-align:left !important;
                    font-size:9pt !important;
                    white-space: nowrap !important;
                    width:100% !important;
                }
                
                div[id*="infoWindowCamcambas"] .Titulo{
                    font-weight:bold;
                    width:100% !important;
                    text-align:left;
                    font-size:12pt !important;
                    color:#5065a1;
                }
                
                div[id*="infoWindowCamcambas"] .Conteudo{
                    font-weight:bold;
                    width:100% !important;
                    text-align:left !important;
                    font-size:10pt !important;
                    color:#000;
                    display:block;
                }
                
                .tblDadosWindowInfo{
                    width:100% !important;
                    padding: 0px !important;
                    box-shadow: 2px 2px 3px #CCC;
                    border:thin solid #DDD;
                    border-spacing: 2px !important;
                    border-collapse:separate;
                }
                
                .tblDadosWindowInfo thead tr th{
                    background-color:#5065a1;
                    border: thin solid #445a9a;
                    color:#FFF!important;
                    text-align:center;
                    font-weight:bold;
                    padding:6px !important;
                }
                
                .tblDadosWindowInfo tbody tr td{
                    background-color:#FFF;
                    color:#666 !important;
                    text-align:center;
                    border:thin solid #DDD;
                    font-weight:500;
                    padding:4px 6px 4px 6px !important;
                }
                
                .TotalGeral{
                    background-color: #000;
                    color:#FFF !important;
                    position:relative !important;
                    display:block !important;
                    margin-top:-1px;
                    border-radius: 3px;
                    width: calc(100% - 12px) !important;
                    margin-left:6px;
                    padding:4px 6px 4px 10px !important;
                    font-weight: bold !important;
                    font-size:10pt;
                    text-transform: uppercase !important;
                }
                
                .TotalGeral label{
                    position: absolute;
                    width:45px;
                    height:18px;
                    border-radius:4px;
                    right:6px;
                    top: 4px;
                    font-size:8pt !important;
                    text-align:center;
                    color:#FFF;
                    font-weight:bold;
                    padding-top:0x !important;
                    background-color:#666;
                    color:#FFF;
                    border:thin solid #999 !important;
                    font-weight: bold !important;
                }
            </style>
        </h:head>
        <h:body>

            <f:metadata>
                <f:viewAction action="#{valores.Persistencia(login.pp, login.satellite)}"/>
                <f:viewAction action="#{valores.mapaContainers2()}"/>
            </f:metadata>

            <p:growl id="msgs"/>

            <ui:composition template = "../assets/template/page.xhtml">	

                <ui:define name="menu">
                    <ui:include src="../assets/template/menu.xhtml" />
                </ui:define>

                <ui:define name="top-menu">
                    <ui:include src="../assets/template/header.xhtml" />  
                </ui:define>            
                
                <ui:define name="infoFilial">
                    <div class="ui-grid-row">
                        <h:outputText value="#{valores.filiais.descricao}" class="negrito"/>
                    </div>
                    <div class="ui-grid-row">
                        #{valores.filiais.endereco}
                    </div>
                    <div class="ui-grid-row">
                        #{valores.filiais.bairro}<h:outputText value=", " rendered="#{valores.filiais.bairro ne null and valores.filiais.bairro ne ''}"/>#{valores.filiais.cidade}/#{valores.filiais.UF}
                    </div>
                </ui:define>                

                <ui:define name="body">
                    <div class="box box-primary" style="font-family: 'Helvetica Neue';font-size: 15px">
                        <h:form id="formMaps">
                            <!--<p:hotkey bind="esc" oncomplete="PF('dlgMaps').hide()"/>-->
                            <p:panel styleClass="painelCadastro" id="cadastrar">
                                <label class="AbrirFiltros" style="left:0px; display: none;"><i class="fa fa-filter"></i>&nbsp;<h:outputText value="#{localemsgs.Filtrar.toUpperCase()}" /></label>
                                <div id="divQuadroResumo" style="left: 23px;">
                                    <h:outputText class="fa fa-times FecharFiltros" title="#{localemsgs.Fechar}" style="display:block" />
                                    
                                    <div class="ItemResumo" style="padding-bottom: 1px !important;">
                                        <label id="lblTituloFiltro">
                                            <i class="fa fa-filter"/>
                                            &nbsp;
                                            <h:outputText value="#{localemsgs.PrazoColeta.toUpperCase()}" />
                                        </label>
                                        <div class="ItemZebrado">
                                            <label class="QdeStatus" cor="R"><i class="fa fa-refresh fa-spin fa-fw"></i></label>
                                            <label>
                                                <input type='checkbox' id='chkColetaVencida' checked="checked" />
                                                &nbsp;
                                                <label for='chkColetaVencida'>
                                                    <h:outputText value=" #{localemsgs.ColetasVencidas}" />
                                                </label>
                                            </label>
                                        </div>
                                        <div class="Item">
                                            <label class="QdeStatus" cor="Y"><i class="fa fa-refresh fa-spin fa-fw"></i></label>
                                            <label>
                                                <input type='checkbox' id='chkColetaVencendo' checked="checked" />
                                                &nbsp;
                                                <label for='chkColetaVencendo'>
                                                    <h:outputText value="#{localemsgs.ColetasHoje}"/>
                                                </label>
                                            </label>
                                        </div>
                                        <div class="ItemZebrado">
                                            <label class="QdeStatus" cor="G" style="padding:1px !important"><i class="fa fa-refresh fa-spin fa-fw"></i></label>
                                            <label>
                                                <input type='checkbox' id='chkColetaVencer' checked="checked" />
                                                &nbsp;
                                                <label for='chkColetaVencer'>
                                                    <h:outputText value="#{localemsgs.ColetasVencer}" />
                                                </label>
                                                    
                                            </label>
                                        </div>
                                        <div class="Item">
                                            <label class="QdeStatus" cor="W" style="padding:1px !important"><i class="fa fa-refresh fa-spin fa-fw"></i></label>
                                            <label>
                                                <input type='checkbox' id='chkColetaSemPrazo' checked="checked" />
                                                &nbsp;
                                                <label for='chkColetaSemPrazo'>
                                                    <h:outputText value="#{localemsgs.SemPrazoColeta}" />
                                                </label>    
                                            </label>
                                        </div>
                                        <label class="TotalGeral">
                                            <h:outputText value="#{localemsgs.Total}" />
                                            <label ref="Total"><i class="fa fa-refresh fa-spin fa-fw"></i></label>
                                        </label>
                                    </div>

                                    <div class="ItemResumo" style="margin-top:10px; padding-bottom: 7px !important;">
                                        <label id="lblTituloFiltro">
                                            <i class="fa fa-filter"/>
                                            &nbsp;
                                            <h:outputText value="#{localemsgs.Bairro.toUpperCase()}"/>
                                        </label>
                                        <div class="ItemZebrado" style="padding:1px !important;">
                                            <p:selectOneMenu id="cboFiltroBairros" value="" style="width:100%;border:none; outline:none; padding:0px !important;">
                                                <f:selectItem itemLabel="[ #{localemsgs.Todos.toUpperCase()} ]" itemValue="T" />
                                            </p:selectOneMenu>

                                        </div>
                                    </div>

                                    <div class="ItemResumo" style="margin-top:10px; padding-bottom: 7px !important;">
                                        <label id="lblTituloFiltro">
                                            <i class="fa fa-filter"/>
                                            &nbsp;
                                            <h:outputText value="#{localemsgs.Cliente.toUpperCase()}"/>
                                        </label>
                                        <div class="ItemZebrado" style="padding:1px !important;">
                                            <p:selectOneMenu id="cboFiltroCliente" value="" style="width:100%;border:none; outline:none; padding:0px !important;">
                                                <f:selectItem itemLabel="[ #{localemsgs.Todos.toUpperCase()} ]" itemValue="T" />
                                            </p:selectOneMenu>
                                        </div>
                                        <div class="ItemZebrado" style="padding:1px !important; margin-top: 3px">
                                            <p:selectOneMenu id="cboFiltroServicoCliente" value="" style="width:100%;border:none; outline:none; padding:0px !important;">
                                                <f:selectItem itemLabel="[ #{localemsgs.Todos.toUpperCase()} ]" itemValue="T" />
                                            </p:selectOneMenu>
                                        </div>
                                    </div>

                                    <div class="ItemResumo" style="margin-top:10px; padding-bottom: 7px !important;">
                                        <label id="lblTituloFiltro">
                                            <i class="fa fa-filter"/>
                                            &nbsp;
                                            <h:outputText value="#{localemsgs.Solicitante.toUpperCase()}"/>
                                        </label>
                                        <div class="ItemZebrado" style="padding:1px !important;">
                                            <p:selectOneMenu id="cboFiltroSolicitante" value="" style="width:100%;border:none; outline:none; padding:0px !important;">
                                                <f:selectItem itemLabel="[ #{localemsgs.Todos.toUpperCase()} ]" itemValue="T" />
                                            </p:selectOneMenu>
                                        </div>
                                    </div>
                                    
                                    <div class="ItemResumo" style="margin-top:10px; padding-bottom: 7px !important;">
                                        <label id="lblTituloFiltro">
                                            <i class="fa fa-filter"/>
                                            &nbsp;
                                            <h:outputText value="#{localemsgs.TempoCliente.toUpperCase()}"/>
                                        </label>
                                        <div class="ItemZebrado" style="padding:1px !important;">
                                            <p:selectOneMenu id="cboFiltroTempo" value="" style="width:100%;border:none; outline:none; padding:0px !important;">
                                                <f:selectItem itemLabel="[ #{localemsgs.Todos.toUpperCase()} ]" itemValue="T" />
                                                <f:selectItem itemLabel="#{localemsgs.Ate.toUpperCase()} 5 #{localemsgs.Dias.toUpperCase()}" itemValue="5" />
                                                <f:selectItem itemLabel="#{localemsgs.Ate.toUpperCase()} 7 #{localemsgs.Dias.toUpperCase()}" itemValue="7" />
                                                <f:selectItem itemLabel="#{localemsgs.Ate.toUpperCase()} 15 #{localemsgs.Dias.toUpperCase()}" itemValue="15" />
                                                <f:selectItem itemLabel="#{localemsgs.Mais15Dias.toUpperCase()}" itemValue="9999" />
                                            </p:selectOneMenu>
                                        </div>
                                    </div>
                                </div>
                                
                                <p:gmap widgetVar="gMapCacambas" id="gMapCacambas" center="#{valores.centroMapa}" zoom="10" type="ROADMAP" 
                                        style="height:85vh" model="#{valores.posicaoRotas}" fitBounds="false" disableDefaultUI="false" styleClass="map">

                                    <p:ajax event="overlaySelect" listener="#{valores.selecionarPinoConteiner}" />

                                    <p:gmapInfoWindow id="infoWindowCamcambas" >
                                        <p:panelGrid columns="1" style="text-align: center; display: block; margin: auto; border:none !important;" >
                                            <h:outputText class="Titulo" value="#{valores.posicaoContainer.IDEquip} - #{valores.posicaoContainer.nred}" style="font-weight: bold; border:none !important;"/>
                                            <label class="Conteudo"><i class="fa fa-map-marker"></i>&nbsp;&nbsp;#{valores.posicaoContainer.ende}</label>
                                            <label class="Conteudo" style="margin-left:15px !important; width:calc(100% - 15px) !important; font-size:7pt !important; color:#505050 !important;">#{valores.posicaoContainer.bairro} - #{valores.posicaoContainer.cidade}/#{valores.posicaoContainer.uf}</label>
                                            <table class="tblDadosWindowInfo">
                                                <thead>
                                                    <tr>
                                                        <th>#{localemsgs.DataHora}</th>
                                                        <th>#{localemsgs.TempoDias}</th>
                                                        <th>Data Prevista</th>
                                                        <th>#{localemsgs.Motorista}</th>
                                                        <th>#{localemsgs.QtdCacambas}</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>#{valores.posicaoContainer.dataEntrega} #{valores.posicaoContainer.hrcheg}</td>
                                                        <td>#{valores.posicaoContainer.tempoDias}</td>
                                                        <td>#{valores.posicaoContainer.dataPrevistaColeta}</td>
                                                        <td>#{valores.posicaoContainer.motorista}</td>
                                                        <td>#{valores.posicaoContainer.qtdeCacambas}</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </p:panelGrid>
                                    </p:gmapInfoWindow>
                                </p:gmap> 
                            </p:panel>
                        </h:form>
                    </div>
            <script type="text/javascript">
                // <![CDATA[
                var gmap;
                var TimerIcons;
                var Bairros = new Array();
                var ClientesFat = new Array();
                var ClientesServ = new Array();
                var Solicitante = new Array();
                
                $(document).ready(function(){
                    clearInterval(TimerIcons);

                    ZerarQuantidades();

                    // Painel já fica aberto por padrão, não precisa do botão
                    $('.AbrirFiltros').css('display', 'none');

                    // Timer verificando se o mapa já foi carregado
                    var tentativas = 0;
                    TimerIcons = setInterval(function(){
                        try{
                            tentativas++;
                            console.log("Tentativa " + tentativas + " de carregar o mapa...");

                            gmap = PF('gMapCacambas').getMap();
                            if(gmap && gmap.markers && gmap.markers.length > 0){
                                console.log("Mapa carregado com " + gmap.markers.length + " marcadores");
                                clearInterval(TimerIcons);
                                CarregarQuantidadeMarcadores();
                            }
                            else if(tentativas > 50) { // Timeout após 30 segundos (50 * 600ms)
                                console.log("Timeout: Mapa não carregou após 30 segundos");
                                clearInterval(TimerIcons);
                            }
                        }
                        catch(e){
                            console.log("Erro ao tentar carregar o mapa: " + e.message);
                        }
                    },600);

                })
                .on('click','.FecharFiltros', function(){
                    $('#divQuadroResumo').stop().animate({
                        'left': '-260px'
                    }, 700);
                    
                    setTimeout(function(){
                        $('.AbrirFiltros').css('display','').stop().animate({
                            'left': '0px'
                        }, 300);
                        $('.FecharFiltros').fadeOut();
                        $('#divQuadroResumo').css('display','none');
                    }, 700);
                })
                .on('click','.AbrirFiltros', function(){
                    $(this).stop().animate({
                        'left': '-50px'
                    }, 300);
                    
                    setTimeout(function(){
                        $('#divQuadroResumo').css('display','').stop().animate({
                            'left': '23px'
                        }, 700);
                        
                        setTimeout(function(){
                            $('.FecharFiltros').fadeIn(1000);
                        }, 800);
                        
                        $('.AbrirFiltros').css('display','none');
                    }, 300);
                })
                .on('change','#divQuadroResumo [type="checkbox"]', function(){
                    console.log("Filtro alterado, reprocessando marcadores...");

                    gmap.markers.forEach(function(Marcador, Index){
                        // Primeiro remove o marcador do mapa
                        Marcador.setMap(null);

                        // Extrair informações do título
                        var titulo = Marcador.title.toString();
                        var corIconeMatch = titulo.match(/CorIcone:\s*(_\w+)/i);
                        var corIcone = corIconeMatch ? corIconeMatch[1] : '';

                        console.log("Processando marcador com cor: " + corIcone);

                        // Verificar se deve mostrar baseado na cor e checkbox
                        var deveExibir = false;

                        if(corIcone === '_vermelho' && $('#chkColetaVencida').prop('checked')) {
                            deveExibir = true;
                        }
                        else if(corIcone === '_amarelo' && $('#chkColetaVencendo').prop('checked')) {
                            deveExibir = true;
                        }
                        else if(corIcone === '_verde' && $('#chkColetaVencer').prop('checked')) {
                            deveExibir = true;
                        }
                        else if(corIcone === '_branco' && $('#chkColetaSemPrazo').prop('checked')) {
                            deveExibir = true;
                        }

                        // Se deve exibir, verificar outros filtros
                        if(deveExibir) {
                            // Verificar filtro de bairro
                            if(!($('div[id*="cboFiltroBairros"] ul li:first').attr('class').indexOf('ui-state-highlight') > -1 ||
                                titulo.split('\n')[5].split(': ')[1].trim() == $('label[id*="cboFiltroBairros_label"]').text())) {
                                deveExibir = false;
                            }

                            // Verificar filtro de cliente
                            if(deveExibir && !($('div[id*="cboFiltroCliente"] ul li:first').attr('class').indexOf('ui-state-highlight') > -1 ||
                                titulo.split('\n')[1].split(': ')[1].trim() == $('label[id*="cboFiltroCliente_label"]').text())) {
                                deveExibir = false;
                            }

                            // Verificar filtro de serviço
                            if(deveExibir && !($('div[id*="cboFiltroServicoCliente"] ul li:first').attr('class').indexOf('ui-state-highlight') > -1 ||
                                titulo.split('\n')[2].split(': ')[1].trim() == $('label[id*="cboFiltroServicoCliente_label"]').text())) {
                                deveExibir = false;
                            }

                            // Verificar filtro de solicitante
                            if(deveExibir && !($('div[id*="cboFiltroSolicitante"] ul li:first').attr('class').indexOf('ui-state-highlight') > -1 ||
                                (titulo.split('\n')[6].split(': ')[1].trim() &&
                                 titulo.split('\n')[6].split(': ')[1].trim() == $('label[id*="cboFiltroSolicitante_label"]').text()))) {
                                deveExibir = false;
                            }

                            // Verificar filtro de tempo
                            if(deveExibir && !FiltrarTempoContainer(titulo.split('\n')[4].split(': ')[1].trim())) {
                                deveExibir = false;
                            }
                        }

                        // Exibir ou ocultar o marcador
                        if(deveExibir) {
                            Marcador.setMap(gmap);
                            console.log("Marcador exibido: " + corIcone);
                        } else {
                            console.log("Marcador ocultado: " + corIcone);
                        }

                    });

                    // Recarregar contadores após filtrar
                    CarregarQuantidadeMarcadores();
                    console.log("Filtros aplicados com sucesso");
                })
                .on('click','div[id*="cboFiltroBairros"] ul li', function(){
                    $('div[id*="cboFiltroBairros"] ul li').removeAttr('data-escape').removeClass('ui-state-disabled ui-state-highlight');
                    $(this).addClass('ui-state-highlight');
                    $('div[id*="cboFiltroBairros"] label[id*="cboFiltroBairros_label"]').text($(this).html());
                    $('#chkColetaVencida').change();
                })
                .on('click','div[id*="cboFiltroCliente"] ul li', function(){
                    $('div[id*="cboFiltroCliente"] ul li').removeAttr('data-escape').removeClass('ui-state-disabled ui-state-highlight');
                    $(this).addClass('ui-state-highlight');
                    $('div[id*="cboFiltroCliente"] label[id*="cboFiltroCliente_label"]').text($(this).html());
                    $('#chkColetaVencida').change();
                })
                .on('click','div[id*="cboFiltroTempo"] ul li', function(){
                    $('div[id*="cboFiltroTempo"] ul li').removeAttr('data-escape').removeClass('ui-state-disabled ui-state-highlight');
                    $(this).addClass('ui-state-highlight');
                    $('div[id*="cboFiltroTempo"] label[id*="cboFiltroTempo_label"]').text($(this).html());
                    $('#chkColetaVencida').change();
                })
                .on('click','div[id*="cboFiltroServicoCliente"] ul li', function(){
                    $('div[id*="cboFiltroServicoCliente"] ul li').removeAttr('data-escape').removeClass('ui-state-disabled ui-state-highlight');
                    $(this).addClass('ui-state-highlight');
                    $('div[id*="cboFiltroServicoCliente"] label[id*="cboFiltroServicoCliente_label"]').text($(this).html());
                    $('#chkColetaVencida').change();
                })
                .on('click','div[id*="cboFiltroSolicitante"] ul li', function(){
                    $('div[id*="cboFiltroSolicitante"] ul li').removeAttr('data-escape').removeClass('ui-state-disabled ui-state-highlight');
                    $(this).addClass('ui-state-highlight');
                    $('div[id*="cboFiltroSolicitante"] label[id*="cboFiltroSolicitante_label"]').text($(this).html());
                    $('#chkColetaVencida').change();
                })
                .on('click','div[id*="cboFiltroCliente"] .ui-selectonemenu-trigger, div[id*="cboFiltroCliente"] .ui-selectonemenu-label', function(){
                    TratarDisplay();
                })
                .on('click','div[id*="cboFiltroBairros"] .ui-selectonemenu-trigger, div[id*="cboFiltroBairros"] .ui-selectonemenu-label', function(){
                    TratarDisplay();
                })
                .on('click','div[id*="cboFiltroCliente"] .ui-selectonemenu-trigger, div[id*="cboFiltroCliente"] .ui-selectonemenu-label', function(){
                    TratarDisplay();
                })
                .on('click','div[id*="cboFiltroTempo"] .ui-selectonemenu-trigger, div[id*="cboFiltroTempo"] .ui-selectonemenu-label', function(){
                    TratarDisplay();
                })
                .on('click','div[id*="cboFiltroSolicitante"] .ui-selectonemenu-trigger, div[id*="cboFiltroSolicitante"] .ui-selectonemenu-label', function(){
                    TratarDisplay();
                })
                .on('click','div[id*="formMaps"]', function(){
                    TratarDisplay();
                })
                .on('keyup','#divQuadroResumo', function(){
                    TratarDisplay();
                })                    
                ;
                               
                function CarregarQuantidadeMarcadores(){
                    ZerarQuantidades();
                    var QtdeVermelho=0;
                    var QtdeAmarelo=0;
                    var QtdeVerde=0;
                    var QtdeBranca=0;
                    Bairros = new Array();
                    ClientesFat= new Array();
                    ClientesServ= new Array();
                    Solicitante= new Array();

                    console.log("Iniciando contagem de marcadores...");

                    try{
                        if(!gmap || !gmap.markers) {
                            console.log("Mapa ou marcadores não disponíveis");
                            return;
                        }

                        console.log("Total de marcadores encontrados: " + gmap.markers.length);

                        gmap.markers.forEach(function(Marcador, Index){
                            // Guardar Bairros
                            if(!Bairros.includes(Marcador.title.toString().split('\n')[5].split(': ')[1].trim()))
                                Bairros.push(Marcador.title.toString().split('\n')[5].split(': ')[1].trim());
                            
                            // Guardar Clientes
                            if(!ClientesFat.includes(Marcador.title.toString().split('\n')[1].split(': ')[1].trim()))
                                ClientesFat.push(Marcador.title.toString().split('\n')[1].split(': ')[1].trim());
                            
                            // Guardar Clientes Serviço
                            if(!ClientesServ.includes(Marcador.title.toString().split('\n')[2].split(': ')[1].trim()))
                                ClientesServ.push(Marcador.title.toString().split('\n')[2].split(': ')[1].trim());
                            
                            // Guardar Solicitantes
                            var solicitanteValue = Marcador.title.toString().split('\n')[6].split(': ')[1].trim();
                            if(solicitanteValue && !Solicitante.includes(solicitanteValue))
                                Solicitante.push(solicitanteValue);
                            
                            // Verificar se o marcador deve ser contado com base nos filtros
                            var passouFiltros = true;
                            
                            // Filtro de bairro
                            if(!($('div[id*="cboFiltroBairros"] ul li:first').attr('class').indexOf('ui-state-highlight') > -1 || 
                                Marcador.title.toString().split('\n')[5].split(': ')[1].trim() == $('label[id*="cboFiltroBairros_label"]').text())) {
                                passouFiltros = false;
                            }
                            
                            // Filtro de cliente
                            if(passouFiltros && !($('div[id*="cboFiltroCliente"] ul li:first').attr('class').indexOf('ui-state-highlight') > -1 || 
                                Marcador.title.toString().split('\n')[1].split(': ')[1].trim() == $('label[id*="cboFiltroCliente_label"]').text())) {
                                passouFiltros = false;
                            }
                            
                            // Filtro de serviço
                            if(passouFiltros && !($('div[id*="cboFiltroServicoCliente"] ul li:first').attr('class').indexOf('ui-state-highlight') > -1 ||  
                                Marcador.title.toString().split('\n')[2].split(': ')[1].trim() == $('label[id*="cboFiltroServicoCliente_label"]').text())) {
                                passouFiltros = false;
                            }
                            
                            // Filtro de solicitante
                            if(passouFiltros && !($('div[id*="cboFiltroSolicitante"] ul li:first').attr('class').indexOf('ui-state-highlight') > -1 ||  
                                (Marcador.title.toString().split('\n')[6].split(': ')[1].trim() && 
                                 Marcador.title.toString().split('\n')[6].split(': ')[1].trim() == $('label[id*="cboFiltroSolicitante_label"]').text()))) {
                                passouFiltros = false;
                            }
                            
                            // Filtro de tempo
                            if(passouFiltros && !FiltrarTempoContainer(Marcador.title.toString().split('\n')[4].split(': ')[1].trim())) {
                                passouFiltros = false;
                            }
                            
                            // Contagem de Containeres
                            if(passouFiltros) {
                                // Extrair informações do título do marcador
                                var titulo = Marcador.title.toString();

                                // Extrair a cor do ícone do título
                                var corIconeMatch = titulo.match(/CorIcone:\s*(_\w+)/i);
                                if(corIconeMatch) {
                                    var corIcone = corIconeMatch[1];
                                    console.log("Cor do ícone encontrada: " + corIcone);

                                    if(corIcone === '_vermelho') {
                                        QtdeVermelho++;
                                        console.log("Incrementando vermelho: " + QtdeVermelho);
                                    }
                                    else if(corIcone === '_amarelo') {
                                        QtdeAmarelo++;
                                        console.log("Incrementando amarelo: " + QtdeAmarelo);
                                    }
                                    else if(corIcone === '_verde') {
                                        QtdeVerde++;
                                        console.log("Incrementando verde: " + QtdeVerde);
                                    }
                                    else if(corIcone === '_branco') {
                                        QtdeBranca++;
                                        console.log("Incrementando branco: " + QtdeBranca);
                                    }
                                    else {
                                        console.log("Cor não reconhecida: " + corIcone);
                                    }
                                } else {
                                    console.log("Cor do ícone não encontrada no título: " + titulo);
                                }
                            }
                        });
                        
                        // Preencher os combos de filtro
                        EscreverBairros();
                        EscreverClientes();
                        EscreverClientesServ();
                        EscreverSolicitantes();
                    }
                    catch(e){
                        console.error("Erro ao carregar marcadores:", e);
                    }
                    
                    // Atualizar os contadores na interface
                    console.log("Contadores finais - Vermelho: " + QtdeVermelho + ", Amarelo: " + QtdeAmarelo + ", Verde: " + QtdeVerde + ", Branco: " + QtdeBranca);

                    $('#divQuadroResumo .QdeStatus[cor="R"]').html(QtdeVermelho);
                    $('#divQuadroResumo .QdeStatus[cor="Y"]').html(QtdeAmarelo);
                    $('#divQuadroResumo .QdeStatus[cor="G"]').html(QtdeVerde);
                    $('#divQuadroResumo .QdeStatus[cor="W"]').html(QtdeBranca);

                    var total = QtdeVermelho + QtdeAmarelo + QtdeVerde + QtdeBranca;
                    $('#divQuadroResumo .TotalGeral label').html(total);

                    console.log("Total geral: " + total);
                }
                
                function EscreverBairros(){
                    Bairros.sort();
                    
                    // Selector Objeto de Bairros
                    var sel = $('select[id*="cboFiltroBairros"]');
                    // Selector Lista Objeto de Bairros
                    var ul = $('div[id*="cboFiltroBairros"] ul'); 
                    // Selector 1º Item da Lista Objeto de Bairros
                    var li = ul.children('li').first();
                    
                    Bairros.forEach(function(Item){
                        // Criar Option
                        var option = $('<option/>').val(Item).text(Item);
                        option.appendTo(sel);

                        // Clonar item existente
                        var lic = li.clone(true); 
                        lic.removeClass('ui-state-disabled ui-state-highlight').attr('data-label', Item).text(Item);
                        lic.appendTo(ul);
                    });
                }
                
                function EscreverClientes(){
                    ClientesFat.sort();
                    
                    // Selector Objeto de Bairros
                    var sel = $('select[id*="cboFiltroCliente"]');
                    // Selector Lista Objeto de Bairros
                    var ul = $('div[id*="cboFiltroCliente"] ul'); 
                    // Selector 1º Item da Lista Objeto de Bairros
                    var li = ul.children('li').first();
                    
                    ClientesFat.forEach(function(Item){
                        // Criar Option
                        var option = $('<option/>').val(Item).text(Item);
                        option.appendTo(sel);

                        // Clonar item existente
                        var lic = li.clone(true); 
                        
                        if(ClientesFat.length > 1){
                            // Inserir Cliente
                            lic.removeClass('ui-state-disabled ui-state-highlight').attr('data-label', Item).text(Item);
                            lic.appendTo(ul);
                        }
                        else{
                            // Inserir Cliente e Selecionar se for único
                            lic.attr('data-label', Item).text(Item);
                            lic.appendTo(ul);
                            $('div[id*="cboFiltroCliente"] label[id*="cboFiltroCliente_label"]').text(Item);
                        }
                    });
                }
                
                function EscreverClientesServ(){
                    ClientesServ.sort();
                    
                    // Selector Objeto de Bairros
                    var sel = $('select[id*="cboFiltroServicoCliente"]');
                    // Selector Lista Objeto de Bairros
                    var ul = $('div[id*="cboFiltroServicoCliente"] ul'); 
                    // Selector 1º Item da Lista Objeto de Bairros
                    var li = ul.children('li').first();
                    
                    ClientesServ.forEach(function(Item){
                        // Criar Option
                        var option = $('<option/>').val(Item).text(Item);
                        option.appendTo(sel);

                        // Clonar item existente
                        var lic = li.clone(true); 
                        lic.removeClass('ui-state-disabled ui-state-highlight').attr('data-label', Item).text(Item);
                        lic.appendTo(ul);
                    });
                }
                
                function EscreverSolicitantes(){
                    Solicitante.sort();
                    
                    // Selector Objeto de Bairros
                    var sel = $('select[id*="cboFiltroSolicitante"]');
                    // Selector Lista Objeto de Bairros
                    var ul = $('div[id*="cboFiltroSolicitante"] ul'); 
                    // Selector 1º Item da Lista Objeto de Bairros
                    var li = ul.children('li').first();
                    
                    Solicitante.forEach(function(Item){
                        if(Item){
                            // Criar Option
                            var option = $('<option/>').val(Item).text(Item);
                            option.appendTo(sel);

                            // Clonar item existente
                            var lic = li.clone(true); 
                            lic.removeClass('ui-state-disabled ui-state-highlight').attr('data-label', Item).text(Item);
                            lic.appendTo(ul);
                        }
                    });
                }
                
                function FiltrarTempoContainer(QdeRef){
                    if($('div[id*="cboFiltroTempo"] ul li:first').attr('class').indexOf('ui-state-highlight') >-1)
                        return true;
                    else{
                        var TempoDias=9999;
                        
                        if($('label[id*="cboFiltroTempo_label"]').text().split(' ')[1].indexOf('15') >-1)
                            TempoDias = 15;
                        else if($('label[id*="cboFiltroTempo_label"]').text().split(' ')[1].indexOf('7') >-1)
                            TempoDias = 7;
                        else if($('label[id*="cboFiltroTempo_label"]').text().split(' ')[1].indexOf('5') >-1)
                            TempoDias = 5;
                        else
                            TempoDias=9999;

                        if(TempoDias != 9999 && eval(QdeRef) <= eval(TempoDias))
                            return true;
                        else if(TempoDias == 9999 && eval(QdeRef) > 15)
                            return true;
                        
                        return false;
                    }
                }
                
                function TratarDisplay(){
                    if($('div[id*="cboFiltroBairros"] ul li[class*="ui-state-highlight"]').length > 1){
                        $('div[id*="cboFiltroBairros"] ul li:first').removeClass('ui-state-disabled ui-state-highlight');
                        $('div[id*="cboFiltroBairros"] label[id*="cboFiltroBairros_label"]').text($('div[id*="cboFiltroBairros"] ul li[class*="ui-state-highlight"]').html());
                    }
                    
                    if($('div[id*="cboFiltroCliente"] ul li[class*="ui-state-highlight"]').length > 1){
                        $('div[id*="cboFiltroCliente"] ul li:first').removeClass('ui-state-disabled ui-state-highlight');
                        $('div[id*="cboFiltroCliente"] label[id*="cboFiltroCliente_label"]').text($('div[id*="cboFiltroCliente"] ul li[class*="ui-state-highlight"]').html());
                    }
                    
                    if($('div[id*="cboFiltroTempo"] ul li[class*="ui-state-highlight"]').length > 1){
                        $('div[id*="cboFiltroTempo"] ul li:first').removeClass('ui-state-disabled ui-state-highlight');
                        $('div[id*="cboFiltroTempo"] label[id*="cboFiltroTempo_label"]').text($('div[id*="cboFiltroTempo"] ul li[class*="ui-state-highlight"]').html());
                    }
                    
                    if($('div[id*="cboFiltroServicoCliente"] ul li[class*="ui-state-highlight"]').length > 1){
                        $('div[id*="cboFiltroServicoCliente"] ul li:first').removeClass('ui-state-disabled ui-state-highlight');
                        $('div[id*="cboFiltroServicoCliente"] label[id*="cboFiltroServicoCliente_label"]').text($('div[id*="cboFiltroServicoCliente"] ul li[class*="ui-state-highlight"]').html());
                    }
                    
                    if($('div[id*="cboFiltroSolicitante"] ul li[class*="ui-state-highlight"]').length > 1){
                        $('div[id*="cboFiltroSolicitante"] ul li:first').removeClass('ui-state-disabled ui-state-highlight');
                        $('div[id*="cboFiltroSolicitante"] label[id*="cboFiltroSolicitante_label"]').text($('div[id*="cboFiltroSolicitante"] ul li[class*="ui-state-highlight"]').html());
                    }
                }
                
                function ZerarQuantidades(){
                    $('#divQuadroResumo .QdeStatus').html('<i class="fa fa-refresh fa-spin fa-fw"></i>');
                };
                // ]]>
            </script>  
                </ui:define>
            </ui:composition>
            
            
            <footer>
                <div class="footer-toggler">
                    <a href="#footer-toggle" id="footer-toggle" >
                        <i class="fa fa-bars" style="font-size: 18px"></i>
                    </a>
                </div>
                <div class="footer-body" id="footer-body">
                    <div>
                        <h:form id="corporativo">
                            <h:outputText value="#{localemsgs.Corporativo}: " />
                            <p:selectBooleanCheckbox value="#{valores.mostrarFiliais}">
                                <p:ajax update="msgs main:tabela cabecalho corporativo" listener="#{valores.MostrarFiliais}" />
                            </p:selectBooleanCheckbox>

                            <p:spacer width="20px"/>

                            <h:outputText value="#{localemsgs.SomenteAtivos}: " />
                            <p:selectBooleanCheckbox value="#{valores.excl}">
                                <p:ajax update="msgs main:tabela cabecalho corporativo" listener="#{valores.SomenteAtivos}" />
                            </p:selectBooleanCheckbox>

                            <p:spacer width="20px"/>

                            <h:outputText value="#{localemsgs.LimparFiltros}: " />
                            <p:selectBooleanCheckbox value="#{valores.limparFiltros}">
                                <p:ajax update="msgs main:tabela cabecalho corporativo" listener="#{valores.LimparFiltros}" />
                            </p:selectBooleanCheckbox>
                        </h:form>
                    </div>
                    <div class="container">
                        <div class="col-sm-3">
                            <table class="footer-time">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-sm-6">
                            <table class="footer-user">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-sm-3">
                            <table class="footer-logos">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}" 
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>   
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
                        
        </h:body>
    </f:view>
</html>
